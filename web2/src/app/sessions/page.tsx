'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Search,
  Filter,
  Eye,
  Trash2,
  Archive,
  Star,
  MessageSquare,
  Code,
  Calendar,
  Clock,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';

interface SessionInfo {
  thread_id: string;
  user_id?: string;
  session_type: 'chat' | 'coding';
  title?: string;
  description?: string;
  tags: string[];
  created_at: string;
  last_accessed: string;
  updated_at: string;
  message_count: number;
  checkpoint_count: number;
  total_tokens?: number;
  status: 'active' | 'archived' | 'deleted';
  is_favorite: boolean;
  metadata: any;
  last_message?: {
    content: string;
    role: 'user' | 'assistant';
    timestamp: string;
  };
}

interface SessionStats {
  total_sessions: number;
  active_sessions: number;
  archived_sessions: number;
  total_messages: number;
  avg_messages_per_session: number;
  most_active_day: string;
  session_types: {
    chat: number;
    coding: number;
  };
}

export default function SessionsPage() {
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [stats, setStats] = useState<SessionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sessionTypeFilter, setSessionTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('active');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedSession, setSelectedSession] = useState<SessionInfo | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const pageSize = 20;

  // 加载会话列表
  const loadSessions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        action: 'list',
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sort_by: 'last_accessed',
        sort_order: 'desc'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (sessionTypeFilter !== 'all') params.append('session_type', sessionTypeFilter);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await fetch(`/api/sessions?${params}`);
      const data = await response.json();

      if (response.ok) {
        setSessions(data.sessions);
        setTotalPages(Math.ceil(data.total / pageSize));
      } else {
        toast.error('加载会话列表失败', { description: data.error });
      }
    } catch (error) {
      toast.error('加载会话列表失败', { description: '网络错误' });
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStats = async () => {
    try {
      const response = await fetch('/api/sessions?action=stats');
      const data = await response.json();
      
      if (response.ok) {
        setStats(data);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 删除会话
  const deleteSession = async (threadId: string) => {
    try {
      const response = await fetch(`/api/sessions?thread_id=${threadId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('会话删除成功');
        loadSessions();
        loadStats();
      } else {
        const data = await response.json();
        toast.error('删除会话失败', { description: data.error });
      }
    } catch (error) {
      toast.error('删除会话失败', { description: '网络错误' });
    }
  };

  // 归档会话
  const archiveSession = async (threadId: string) => {
    try {
      const response = await fetch(`/api/sessions?thread_id=${threadId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'archived' })
      });

      if (response.ok) {
        toast.success('会话已归档');
        loadSessions();
        loadStats();
      } else {
        const data = await response.json();
        toast.error('归档会话失败', { description: data.error });
      }
    } catch (error) {
      toast.error('归档会话失败', { description: '网络错误' });
    }
  };

  // 切换收藏状态
  const toggleFavorite = async (threadId: string, isFavorite: boolean) => {
    try {
      const response = await fetch(`/api/sessions?thread_id=${threadId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_favorite: !isFavorite })
      });

      if (response.ok) {
        toast.success(isFavorite ? '已取消收藏' : '已添加收藏');
        loadSessions();
      } else {
        const data = await response.json();
        toast.error('操作失败', { description: data.error });
      }
    } catch (error) {
      toast.error('操作失败', { description: '网络错误' });
    }
  };

  // 查看会话详情
  const viewSessionDetails = async (threadId: string) => {
    try {
      const response = await fetch(`/api/sessions?thread_id=${threadId}&include_history=true&history_limit=20`);
      const data = await response.json();
      
      if (response.ok) {
        setSelectedSession(data);
        setShowDetails(true);
      } else {
        toast.error('加载会话详情失败', { description: data.error });
      }
    } catch (error) {
      toast.error('加载会话详情失败', { description: '网络错误' });
    }
  };

  useEffect(() => {
    loadSessions();
    loadStats();
  }, [currentPage, searchTerm, sessionTypeFilter, statusFilter]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const getSessionTypeIcon = (type: string) => {
    return type === 'chat' ? <MessageSquare className="w-4 h-4" /> : <Code className="w-4 h-4" />;
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, { bg: string; text: string; icon: string }> = {
      active: { bg: 'bg-green-50 border-green-200', text: 'text-green-700', icon: '🟢' },
      archived: { bg: 'bg-orange-50 border-orange-200', text: 'text-orange-700', icon: '📦' },
      deleted: { bg: 'bg-red-50 border-red-200', text: 'text-red-700', icon: '🗑️' }
    };

    const variant = variants[status] || { bg: 'bg-gray-50 border-gray-200', text: 'text-gray-700', icon: '❓' };
    const label = status === 'active' ? '活跃' : status === 'archived' ? '已归档' : '已删除';

    return (
      <Badge className={`${variant.bg} ${variant.text} border font-medium px-2.5 py-1 text-xs`}>
        <span className="mr-1">{variant.icon}</span>
        {label}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50/30">
      <div className="container mx-auto p-4 md:p-6 space-y-6">
        {/* 页面标题 */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">会话管理</h1>
            <p className="text-gray-600 mt-1">管理和查看所有聊天会话</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                loadSessions();
                loadStats();
              }}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新数据
            </Button>
          </div>
        </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">总会话数</CardTitle>
              <div className="p-2 bg-blue-100 rounded-full">
                <BarChart3 className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-700">{stats.total_sessions}</div>
              <p className="text-xs text-gray-500 mt-1">所有会话总数</p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">活跃会话</CardTitle>
              <div className="p-2 bg-green-100 rounded-full">
                <MessageSquare className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-700">{stats.active_sessions}</div>
              <p className="text-xs text-gray-500 mt-1">正在使用的会话</p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">总消息数</CardTitle>
              <div className="p-2 bg-purple-100 rounded-full">
                <MessageSquare className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-700">{stats.total_messages}</div>
              <p className="text-xs text-gray-500 mt-1">所有消息总数</p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">平均消息数</CardTitle>
              <div className="p-2 bg-orange-100 rounded-full">
                <BarChart3 className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-700">{stats.avg_messages_per_session}</div>
              <p className="text-xs text-gray-500 mt-1">每个会话平均</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 搜索和过滤器 */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <div className="p-2 bg-gray-100 rounded-lg">
              <Filter className="w-5 h-5 text-gray-600" />
            </div>
            搜索和过滤
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="搜索会话标题或描述..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Select value={sessionTypeFilter} onValueChange={setSessionTypeFilter}>
                <SelectTrigger className="w-full sm:w-40 h-11 border-gray-200 focus:border-blue-500">
                  <SelectValue placeholder="会话类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="chat">💬 聊天</SelectItem>
                  <SelectItem value="coding">💻 编程</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-36 h-11 border-gray-200 focus:border-blue-500">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="active">🟢 活跃</SelectItem>
                  <SelectItem value="archived">📦 已归档</SelectItem>
                  <SelectItem value="deleted">🗑️ 已删除</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setSessionTypeFilter('all');
                  setStatusFilter('active');
                }}
                className="h-11 px-4 border-gray-200 hover:bg-gray-50"
              >
                清除筛选
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 会话列表 */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">会话列表</CardTitle>
              <CardDescription className="mt-1 text-gray-600">
                共 {sessions.length} 个会话，第 {currentPage} 页，共 {totalPages} 页
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                loadSessions();
                loadStats();
              }}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
              <div className="text-gray-700">加载中...</div>
            </div>
          ) : sessions.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <MessageSquare className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-gray-700 text-lg mb-2">没有找到会话</div>
              <div className="text-sm text-gray-600">尝试调整搜索条件或创建新的会话</div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="rounded-lg border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50/50">
                        <TableHead className="font-semibold text-gray-700 min-w-[250px]">会话信息</TableHead>
                        <TableHead className="font-semibold text-gray-700 min-w-[100px]">类型</TableHead>
                        <TableHead className="font-semibold text-gray-700 min-w-[100px]">状态</TableHead>
                        <TableHead className="font-semibold text-gray-700 text-center min-w-[80px]">消息数</TableHead>
                        <TableHead className="font-semibold text-gray-700 min-w-[150px]">最后访问</TableHead>
                        <TableHead className="font-semibold text-gray-700 text-center min-w-[120px]">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                <TableBody>
                  {sessions.map((session) => (
                    <TableRow
                      key={session.thread_id}
                      className="hover:bg-gray-50/50 transition-colors"
                    >
                      <TableCell className="py-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-gray-900 line-clamp-1">
                              {session.title || `${session.session_type === 'chat' ? '聊天' : '编程'}会话`}
                            </span>
                            {session.is_favorite && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current flex-shrink-0" />
                            )}
                          </div>
                          <div className="text-xs text-gray-500 font-mono">
                            ID: {session.thread_id}
                          </div>
                          {session.last_message && (
                            <div className="text-sm text-gray-600 truncate max-w-xs">
                              {session.last_message.content.slice(0, 50)}...
                            </div>
                          )}
                          {session.tags.length > 0 && (
                            <div className="flex gap-1 flex-wrap">
                              {session.tags.slice(0, 3).map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs h-5 px-2 bg-blue-50 text-blue-700 border-blue-200">
                                  {tag}
                                </Badge>
                              ))}
                              {session.tags.length > 3 && (
                                <Badge variant="outline" className="text-xs h-5 px-2 bg-gray-50 text-gray-600 border-gray-200">
                                  +{session.tags.length - 3}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>

                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 rounded-lg bg-gray-100">
                            {getSessionTypeIcon(session.session_type)}
                          </div>
                          <span className="text-sm font-medium text-gray-700">
                            {session.session_type === 'chat' ? '聊天' : '编程'}
                          </span>
                        </div>
                      </TableCell>

                      <TableCell className="py-4">
                        {getStatusBadge(session.status)}
                      </TableCell>

                      <TableCell className="py-4">
                        <div className="text-center">
                          <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-50 text-blue-700 rounded-full text-sm font-medium">
                            {session.message_count}
                          </span>
                        </div>
                      </TableCell>

                      <TableCell className="py-4">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <div className="flex flex-col">
                            <span className="text-sm">{formatDate(session.last_accessed).split(' ')[0]}</span>
                            <span className="text-xs text-gray-400">{formatDate(session.last_accessed).split(' ')[1]}</span>
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewSessionDetails(session.thread_id)}
                            className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
                            title="查看详情"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleFavorite(session.thread_id, session.is_favorite)}
                            className={`h-8 w-8 p-0 hover:bg-yellow-50 ${
                              session.is_favorite
                                ? 'text-yellow-500 hover:text-yellow-600'
                                : 'hover:text-yellow-600'
                            }`}
                            title={session.is_favorite ? "取消收藏" : "添加收藏"}
                          >
                            <Star className={`w-4 h-4 ${session.is_favorite ? 'fill-current' : ''}`} />
                          </Button>

                          {session.status === 'active' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => archiveSession(session.thread_id)}
                              className="h-8 w-8 p-0 hover:bg-orange-50 hover:text-orange-600"
                              title="归档会话"
                            >
                              <Archive className="w-4 h-4" />
                            </Button>
                          )}

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteSession(session.thread_id)}
                            className="h-8 w-8 p-0 text-gray-400 hover:bg-red-50 hover:text-red-600"
                            title="删除会话"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                  </Table>
                </div>
              </div>

              {/* 分页控件 */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4 px-2">
                  <div className="text-sm text-gray-600">
                    第 {currentPage} 页，共 {totalPages} 页
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="h-9"
                    >
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="h-9"
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 会话详情对话框 */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>会话详情</DialogTitle>
            <DialogDescription>
              查看会话的详细信息和历史记录
            </DialogDescription>
          </DialogHeader>

          {selectedSession && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">基本信息</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>会话ID:</strong> {selectedSession.thread_id}</div>
                    <div><strong>标题:</strong> {selectedSession.title || '无标题'}</div>
                    <div><strong>类型:</strong> {selectedSession.session_type === 'chat' ? '聊天' : '编程'}</div>
                    <div><strong>状态:</strong> {getStatusBadge(selectedSession.status)}</div>
                    <div><strong>用户ID:</strong> {selectedSession.user_id || '未知'}</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">统计信息</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>消息数:</strong> {selectedSession.message_count}</div>
                    <div><strong>检查点数:</strong> {selectedSession.checkpoint_count}</div>
                    <div><strong>Token数:</strong> {selectedSession.total_tokens || '未知'}</div>
                    <div><strong>是否收藏:</strong> {selectedSession.is_favorite ? '是' : '否'}</div>
                  </div>
                </div>
              </div>

              {/* 时间信息 */}
              <div>
                <h4 className="font-medium mb-2">时间信息</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <strong>创建时间:</strong><br />
                    {formatDate(selectedSession.created_at)}
                  </div>
                  <div>
                    <strong>最后访问:</strong><br />
                    {formatDate(selectedSession.last_accessed)}
                  </div>
                  <div>
                    <strong>更新时间:</strong><br />
                    {formatDate(selectedSession.updated_at)}
                  </div>
                </div>
              </div>

              {/* 标签 */}
              {selectedSession.tags && selectedSession.tags.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">标签</h4>
                  <div className="flex gap-2 flex-wrap">
                    {selectedSession.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* 最后一条消息 */}
              {selectedSession.last_message && (
                <div>
                  <h4 className="font-medium mb-2">最后一条消息</h4>
                  <div className="bg-muted p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant={selectedSession.last_message.role === 'user' ? 'default' : 'secondary'}>
                        {selectedSession.last_message.role === 'user' ? '用户' : '助手'}
                      </Badge>
                      <span className="text-sm text-gray-600">
                        {formatDate(selectedSession.last_message.timestamp)}
                      </span>
                    </div>
                    <div className="text-sm">
                      {selectedSession.last_message.content}
                    </div>
                  </div>
                </div>
              )}

              {/* 历史记录 */}
              {(selectedSession as any).history && (
                <div>
                  <h4 className="font-medium mb-2">历史记录</h4>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {(selectedSession as any).history.map((checkpoint: any, index: number) => (
                      <div key={index} className="bg-muted p-3 rounded-lg text-sm">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium">检查点 {checkpoint.step}</span>
                          <span className="text-gray-600">
                            {checkpoint.timestamp ? formatDate(checkpoint.timestamp) : '未知时间'}
                          </span>
                        </div>
                        <div className="text-gray-600">
                          来源: {checkpoint.source || '未知'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 元数据 */}
              {selectedSession.metadata && Object.keys(selectedSession.metadata).length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">元数据</h4>
                  <pre className="bg-muted p-3 rounded-lg text-sm overflow-x-auto">
                    {JSON.stringify(selectedSession.metadata, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </div>
  );
}
